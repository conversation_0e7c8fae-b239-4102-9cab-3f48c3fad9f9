extends Node

# Signals for state changes
signal player_data_updated(player_data: Dictionary)
signal territory_updated(territory_data: Dictionary) # Example, adjust as needed
signal inventory_updated(inventory_data: Dictionary) # Example, adjust as needed
signal location_updated(latitude: float, longitude: float)

# Player related state
var player_id: int = -1
var player_nickname: String = ""
var player_avatar_url: String = ""
var player_resources: Array = [] # e.g., [{type:{slug:'food', name:'<PERSON><PERSON><PERSON><PERSON>'},amount:10}, ...]
var player_equipment: Dictionary = {} # Example structure
var player_range: int = 0
var player_hero: Dictionary = {}

# Map/World related state
var current_latitude: float = 0.0
var current_longitude: float = 0.0
var owned_territories: Array = [] # Array of territory IDs or objects
var nearby_resources: Array = [] # Array of resource node data
var nearby_territories: Array = [] # Array of territory data
var follow_gps = true

func _ready() -> void:
	GpsService.location_updated.connect(update_player_position)
	KokumeApi.player_info_loaded.connect(_set_player_data)


func _set_player_data(data: Dictionary) -> void:
	var player_info = data.get("player", {})
	player_id = player_info.get("id", -1)
	player_nickname = player_info.get("nickname", "Unknown")
	player_range = player_info.get("range", 0)
	# Assuming avatar URL might be separate or nested differently
	player_avatar_url = data.get("avatar_url", "")
	# Assuming resources might be part of the player data payload
	player_resources = data.get("resources", {})
	player_hero = player_info.get('hero', {})
	player_data_updated.emit(get_player_state())


func update_player_position(latitude: float, longitude: float, _accuracy: float) -> void:
	current_latitude = latitude
	current_longitude = longitude


func update_player_resources(new_resources: Array) -> void:
	player_resources = new_resources # Or merge/update based on API response structure
	player_data_updated.emit(get_player_state()) # Emit general player update or a specific resource signal


func get_player_state() -> Dictionary:
	return {
		"id": player_id,
		"nickname": player_nickname,
		"avatar_url": player_avatar_url,
		"resources": player_resources,
		"equipment": player_equipment,
		"owned_territories": owned_territories, # Maybe only IDs here
		"range": player_range
	}

# --- Location State Management ---

func update_player_location(lat: float, lon: float) -> void:
	current_latitude = lat
	current_longitude = lon
	location_updated.emit(lat, lon)
	# Potentially trigger fetching nearby objects based on new location


func get_player_location() -> Vector2:
	return Vector2(current_latitude, current_longitude)

# --- World State Management ---

func update_nearby_territories(territories: Array) -> void:
	nearby_territories = territories
	# Emit signal if needed: nearby_territories_updated.emit(nearby_territories)


func update_nearby_resources(resources: Array) -> void:
	nearby_resources = resources
	# Emit signal if needed: nearby_resources_updated.emit(nearby_resources)


func add_owned_territory(territory_id_or_data) -> void:
	# Add logic to prevent duplicates if necessary
	owned_territories.append(territory_id_or_data)
	territory_updated.emit({"action": "add", "data": territory_id_or_data}) # Example signal payload


func remove_owned_territory(territory_id) -> void:
	# Find and remove territory
	for i in range(owned_territories.size()):
		# Adjust comparison based on what's stored (ID or Dictionary)
		var current_territory = owned_territories[i]
		var current_id = -1
		if typeof(current_territory) == TYPE_DICTIONARY:
			current_id = current_territory.get("id", -1)
		elif typeof(current_territory) == TYPE_INT:
			current_id = current_territory

		if current_id == territory_id:
			owned_territories.remove_at(i)
			territory_updated.emit({"action": "remove", "id": territory_id}) # Example signal payload
			break


# --- Resources ---

func check_player_has_enough_resources(resource_slug: String, amount: int) -> bool:
	for resource in player_resources:
		if resource.type.slug == resource_slug:
			return resource.amount >= amount
	return false

# --- Hero ---

func player_has_hero() -> bool:
	# For now, just return false as we don't have hero implementation yet
	# This will be updated when hero functionality is implemented
	return false


# --- Range ---

func is_in_range(lat: float, lon: float) -> bool:
	var dLat = deg_to_rad(lat - current_latitude)
	var dLon = deg_to_rad(lon - current_longitude)
	var lat1_rad = deg_to_rad(current_latitude)
	var lat2_rad = deg_to_rad(lat)

	var a = sin(dLat / 2.0) * sin(dLat / 2.0) + sin(dLon / 2.0) * sin(dLon / 2.0) * cos(lat1_rad) * cos(lat2_rad)
	var c = 2.0 * atan2(sqrt(a), sqrt(1.0 - a))

	var uuu = MapViewer.EARTH_RADIUS_METERS * c
	return uuu <= player_range


# --- Inventory/Equipment Management (Example Stubs) ---

func update_inventory(data: Dictionary) -> void:
	# Replace or merge inventory data
	inventory_updated.emit(data)


func update_equipment(data: Dictionary) -> void:
	player_equipment = data
	player_data_updated.emit(get_player_state())


# --- Reset State ---
func reset_state() -> void:
	player_id = -1
	player_nickname = ""
	player_avatar_url = ""
	player_resources = []
	player_equipment = {}
	current_latitude = 0.0
	current_longitude = 0.0
	owned_territories = []
	nearby_resources = []
	nearby_territories = []
	Logger.info("GameState", "GameState reset.")
