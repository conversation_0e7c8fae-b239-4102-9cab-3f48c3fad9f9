extends Node

# Signal emitted when location is updated
signal location_updated(latitude: float, longitude: float, accuracy: float)

# Signal emitted when GPS status changes (e.g., initializing, active, error)
signal gps_status_changed(status: String) # Consider using an enum later

const PERMISSION_FINE_LOCATION = "android.permission.ACCESS_FINE_LOCATION"

var gps_provider = null
var is_listening: bool = false # Tracks if the *service* is intended to be active (real or simulated)
var current_status: String = "INITIALIZING"
var auto_start_requested: bool = false # Flag to track if auto-start was requested
var permission_check_timer: Timer = null # Timer for periodic permission checks

# GPS plugin uses standard API

# --- Simulation State ---
var simulate_gps: bool = false
var simulated_latitude: float = 0.0 # Default simulation start point (e.g., city center)
var simulated_longitude: float = 0.0
var simulated_accuracy: float = 5.0 # Fixed accuracy for simulated points
var simulated_timer: Timer = null

func _ready():
	# Connect to our own status changes to handle auto-start
	gps_status_changed.connect(_on_status_changed_internal)
	# Defer the initialization logic to avoid potential startup crashes
	call_deferred("_initialize_gps")


func _exit_tree():
	# Clean up timers
	if permission_check_timer:
		permission_check_timer.queue_free()
	if simulated_timer:
		simulated_timer.queue_free()


func _initialize_gps():
	Logger.info("GpsService", "Initializing GPS Service...")
	Logger.info("GpsService", "Platform: %s, Android: %s" % [OS.get_name(), OS.has_feature("android")])

	# Check for the GPS plugin singleton
	if Engine.has_singleton("PraxisMapperGPSPlugin"):
		gps_provider = Engine.get_singleton("PraxisMapperGPSPlugin")
		Logger.info("GpsService", "GPS Plugin singleton found: %s" % gps_provider)

		# Connect signals only if provider exists
		if gps_provider:
			# Check if the provider has the expected methods and signals
			if not gps_provider.has_method("StartListening"):
				Logger.error("GpsService", "GPS Plugin missing StartListening method!")
				_update_status("ERROR_PLUGIN_INVALID")
				return

			if not gps_provider.has_signal("onLocationUpdates"):
				Logger.error("GpsService", "GPS Plugin missing onLocationUpdates signal!")
				_update_status("ERROR_PLUGIN_INVALID")
				return

			# Connect to GPS signals
			if not gps_provider.is_connected("onLocationUpdates", _on_location_updated):
				gps_provider.onLocationUpdates.connect(_on_location_updated)
				Logger.info("GpsService", "Connected to onLocationUpdates signal.")

			if gps_provider.has_signal("onLastKnownLocation") and not gps_provider.is_connected("onLastKnownLocation", _on_last_known_location):
				gps_provider.onLastKnownLocation.connect(_on_last_known_location)
				Logger.info("GpsService", "Connected to onLastKnownLocation signal.")

			if gps_provider.has_signal("onLocationError") and not gps_provider.is_connected("onLocationError", _on_location_error):
				gps_provider.onLocationError.connect(_on_location_error)
				Logger.info("GpsService", "Connected to onLocationError signal.")
		else:
			Logger.error("GpsService", "GPS Plugin singleton is null!")
			_update_status("ERROR_PLUGIN_MISSING")
			return

		Logger.info("GpsService", "GPS Plugin initialized successfully.")
	else:
		Logger.error("GpsService", "PraxisMapperGPSPlugin singleton not found. Available singletons: %s" % Engine.get_singleton_list())
		_update_status("ERROR_PLUGIN_MISSING")
		return

	# Handle permissions
	if OS.has_feature("android"):
		Logger.info("GpsService", "Android platform detected, checking permissions...")
		# Check and request permissions asynchronously
		check_and_request_permissions()
	else:
		# Handle non-Android platforms (e.g., desktop testing)
		Logger.info("GpsService", "Non-Android platform (%s). GPS permissions not applicable." % OS.get_name())
		auto_start_requested = true # Mark that we should auto-start when ready
		_update_status("READY") # Ready to start (real or simulated)


func check_and_request_permissions() -> void:
	if OS.has_feature("android"):
		# Use get_granted_permissions().has() instead of check_permission()
		if not OS.get_granted_permissions().has(PERMISSION_FINE_LOCATION):
			Logger.info("GpsService", "Requesting location permissions...")
			_update_status("REQUESTING_PERMISSIONS")

			# Request permissions asynchronously
			# Godot 4.4+ requests *all* permissions from the export preset, no args needed.
			OS.request_permissions()

			# Wait a bit for the permission dialog to be processed
			await get_tree().create_timer(0.5).timeout

			# Start a timer to check permission status periodically after request
			_start_permission_check_after_request()
		else:
			Logger.info("GpsService", "Location permissions already granted.")
			auto_start_requested = true # Mark that we should auto-start when ready
			_update_status("READY")
	else:
		Logger.info("GpsService", "Permissions check skipped on non-Android platform.")
		auto_start_requested = true # Mark that we should auto-start when ready
		_update_status("READY")


# --- Public Control ---

func start_listening() -> void:
	# This function now attempts to start the REAL GPS provider
	# Simulation activation is handled by set_gps_simulation(true)
	if simulate_gps:
		Logger.info("GpsService", "Start listening called, but simulation is active. Use set_gps_simulation(true) to manage.")
		# Ensure status is ACTIVE if simulation is on
		if not is_listening:
			is_listening = true
			_update_status("ACTIVE")
			# Emit initial simulated location
			location_updated.emit(simulated_latitude, simulated_longitude, simulated_accuracy)
		return

	if not gps_provider:
		Logger.error("GpsService", "Cannot start listening: GPS provider not available.")
		_update_status("ERROR_PLUGIN_MISSING")
		return
	if not is_listening:
		# Ensure permissions are granted before starting (double-check)
		if OS.has_feature("android") and not OS.get_granted_permissions().has(PERMISSION_FINE_LOCATION):
			Logger.error("GpsService", "Cannot start listening: Location permission not granted.")
			_update_status("ERROR_PERMISSION_DENIED")
			check_and_request_permissions() # Attempt to re-request
			return

		Logger.info("GpsService", "Starting REAL GPS listening...")

		# Try to start GPS listening using StartListening method
		var result = gps_provider.StartListening()
		Logger.info("GpsService", "StartListening called, result: %s (type: %s)" % [result, typeof(result)])

		# Check the result properly - adjust based on actual plugin behavior
		# Many GPS plugins return null on success, so we treat null as success
		if result == null or (typeof(result) == TYPE_BOOL and result == true) or (typeof(result) == TYPE_INT and result == OK):
			is_listening = true
			_update_status("ACTIVE")
			Logger.info("GpsService", "GPS listening started successfully.")
		else:
			Logger.error("GpsService", "Failed to start REAL GPS listening. Plugin StartListening returned: %s (type: %s)" % [result, typeof(result)])
			_update_status("ERROR_START_FAILED")
			is_listening = false # Ensure state is correct on failure
	elif is_listening:
		Logger.info("GpsService", "REAL GPS already listening.")


func stop_listening() -> void:
	# This function now attempts to stop the REAL GPS provider
	# Simulation deactivation is handled by set_gps_simulation(false)
	if simulate_gps:
		Logger.info("GpsService", "Stop listening called, but simulation is active. Use set_gps_simulation(false) to deactivate.")
		# If simulation is active, stopping means setting service inactive
		if is_listening:
			is_listening = false
			_update_status("INACTIVE")
		return

	if gps_provider and is_listening:
		Logger.info("GpsService", "Stopping REAL GPS listening.")
		if gps_provider.has_method("StopListening"):
			gps_provider.StopListening()
			Logger.info("GpsService", "StopListening called.")
		else:
			Logger.warning("GpsService", "GPS plugin missing StopListening method.")
		is_listening = false
		_update_status("INACTIVE")
	elif not is_listening:
		Logger.info("GpsService", "GPS Service was already inactive.")


func _on_location_updated(data: Dictionary) -> void:
	Logger.debug("GpsService", "Location update received. Is listening: %s, Data: %s" % [is_listening, data])

	if is_listening:
		# Validate data structure
		if not data.has("latitude") or not data.has("longitude"):
			Logger.error("GpsService", "Invalid location data received: missing lat/lon. Data: %s" % data)
			return

		var lat = data.latitude
		var lon = data.longitude
		var acc = data.get('accuracy', 0)
		var spd = data.get('speed', 0)

		# Validate coordinate values
		if typeof(lat) != TYPE_FLOAT and typeof(lat) != TYPE_INT:
			Logger.error("GpsService", "Invalid latitude type: %s (value: %s)" % [typeof(lat), lat])
			return
		if typeof(lon) != TYPE_FLOAT and typeof(lon) != TYPE_INT:
			Logger.error("GpsService", "Invalid longitude type: %s (value: %s)" % [typeof(lon), lon])
			return

		Logger.info("GpsService", "Location Update: lat %s, lon %s, acc %s, spd %s" % [lat, lon, acc, spd])

		# Update API
		KokumeApi.update_player_status({
			"latLng": {
				"lat": lat,
				"lng": lon
			},
			"accuracy": acc,
			"speed": spd,
		})

		# Emit signal for other components
		location_updated.emit(lat, lon, acc)
	else:
		Logger.warning("GpsService", "Location update received but service is not listening. Ignoring.")


func _on_last_known_location(data: Dictionary) -> void:
	Logger.info("GpsService", "Last known location received: %s" % data)
	# Process last known location the same way as regular updates
	_on_location_updated(data)


func _on_location_error(error_message: String) -> void:
	Logger.error("GpsService", "GPS location error: %s" % error_message)
	_update_status("ERROR_GPS_FAILED")


func _update_status(new_status: String) -> void:
	if current_status != new_status:
		current_status = new_status
		gps_status_changed.emit(current_status)
		Logger.info("GpsService", "GPS Status: %s" % current_status)


func _on_status_changed_internal(status: String) -> void:
	# Auto-start GPS when status becomes READY and auto-start was requested
	if status == "READY" and auto_start_requested and not is_listening and not simulate_gps:
		Logger.info("GpsService", "Auto-starting GPS after permission grant...")
		auto_start_requested = false # Reset flag to prevent multiple auto-starts
		# Use call_deferred to avoid potential issues with signal handling
		call_deferred("start_listening")


func get_status() -> String:
	return current_status

func is_active() -> bool:
	# Service is considered active if listening (real or simulated)
	return is_listening


# --- Simulation Control ---

func set_gps_simulation(enabled: bool) -> void:
	if simulate_gps == enabled:
		Logger.info("GpsService", "GPS simulation already in the desired state (%s)." % enabled)
		return

	simulate_gps = enabled
	Logger.info("GpsService", "GPS Simulation set to: %s" % enabled)

	if enabled:
		# Stop real GPS if it was running
		if gps_provider and is_listening:
			Logger.info("GpsService", "Stopping real GPS provider for simulation.")
			if gps_provider.has_method("StopListening"):
				gps_provider.StopListening()

		# Set service state to active (simulated)
		is_listening = true
		_update_status("ACTIVE")
		# Emit current simulated location immediately
		location_updated.emit(simulated_latitude, simulated_longitude, simulated_accuracy)
		
		simulated_timer = Timer.new()
		simulated_timer.autostart = true
		simulated_timer.wait_time = 3 # 3s
		simulated_timer.timeout.connect(_update_simulated_location)
		add_child(simulated_timer)
		
	else:
		# Disabling simulation - attempt to start real GPS
		is_listening = false # Mark as not listening initially
		_update_status("INACTIVE") # Temporarily inactive while starting real GPS
		Logger.info("GpsService", "Attempting to start real GPS after disabling simulation...")
		start_listening() # This will handle permissions and provider start


func set_simulated_location(lat: float, lon: float) -> void:
	simulated_latitude = lat
	simulated_longitude = lon
	Logger.info("GpsService", "Simulated location set to: Lat %f, Lon %f" % [lat, lon])

	# If simulation is active, emit the update immediately
	if simulate_gps and is_listening:
		location_updated.emit(simulated_latitude, simulated_longitude, simulated_accuracy)

func _update_simulated_location() -> void:
	_on_location_updated({
		"latitude": simulated_latitude, 
		"longitude": simulated_longitude, 
		"accuracy": 2
		})


func is_gps_simulated() -> bool:
	return simulate_gps


func get_simulated_location() -> Vector2:
	return Vector2(simulated_latitude, simulated_longitude)


# Debug and testing functions
func get_debug_info() -> Dictionary:
	return {
		"status": current_status,
		"is_listening": is_listening,
		"simulate_gps": simulate_gps,
		"gps_provider_exists": gps_provider != null,
		"platform": OS.get_name(),
		"is_android": OS.has_feature("android"),
		"permissions_granted": OS.has_feature("android") and OS.get_granted_permissions().has(PERMISSION_FINE_LOCATION),
		"available_singletons": Engine.get_singleton_list(),
		"auto_start_requested": auto_start_requested,
		"has_start_listening": gps_provider != null and gps_provider.has_method("StartListening"),
		"has_stop_listening": gps_provider != null and gps_provider.has_method("StopListening"),
		"has_location_updates_signal": gps_provider != null and gps_provider.has_signal("onLocationUpdates")
	}


func force_restart_gps() -> void:
	Logger.info("GpsService", "Force restarting GPS service...")
	stop_listening()
	await get_tree().create_timer(1.0).timeout
	start_listening()


func test_gps_plugin() -> void:
	Logger.info("GpsService", "Testing GPS plugin...")
	var debug_info = get_debug_info()
	for key in debug_info:
		Logger.info("GpsService", "Debug - %s: %s" % [key, debug_info[key]])

	if gps_provider:
		var methods = gps_provider.get_method_list()
		var signals = gps_provider.get_signal_list()

		Logger.info("GpsService", "GPS Provider has %d methods:" % methods.size())
		for method in methods:
			Logger.info("GpsService", "  Method: %s (args: %d)" % [method.name, method.args.size()])

		Logger.info("GpsService", "GPS Provider has %d signals:" % signals.size())
		for signal_info in signals:
			Logger.info("GpsService", "  Signal: %s (args: %d)" % [signal_info.name, signal_info.args.size()])

		# Test if plugin has any callable methods at all
		Logger.info("GpsService", "Testing direct method calls...")

		# Try to call some methods to see what happens
		var test_methods = ["StartListening", "startListening", "start", "enable", "requestLocationUpdates"]
		for method_name in test_methods:
			if gps_provider.has_method(method_name):
				Logger.info("GpsService", "Plugin HAS method: %s" % method_name)
				var result = gps_provider.call(method_name)
				Logger.info("GpsService", "Called %s, result: %s" % [method_name, result])
			else:
				Logger.info("GpsService", "Plugin does NOT have method: %s" % method_name)


func _start_permission_check_after_request() -> void:
	# Start a timer to check permission status after permission request
	if permission_check_timer:
		permission_check_timer.queue_free()

	permission_check_timer = Timer.new()
	permission_check_timer.wait_time = 1.0 # Check every 1 second after request
	permission_check_timer.timeout.connect(_check_permissions_after_request)
	permission_check_timer.autostart = true
	add_child(permission_check_timer)
	Logger.info("GpsService", "Started permission checking after request...")


func _start_permission_check_timer() -> void:
	# Start a timer to periodically check if permissions were granted externally
	if permission_check_timer:
		permission_check_timer.queue_free()

	permission_check_timer = Timer.new()
	permission_check_timer.wait_time = 5.0 # Check every 5 seconds
	permission_check_timer.timeout.connect(_check_permissions_periodically)
	permission_check_timer.autostart = true
	add_child(permission_check_timer)
	Logger.info("GpsService", "Started periodic permission checking...")


func _check_permissions_after_request() -> void:
	# Check permission status after permission request dialog
	if current_status == "REQUESTING_PERMISSIONS" and OS.has_feature("android"):
		if OS.get_granted_permissions().has(PERMISSION_FINE_LOCATION):
			Logger.info("GpsService", "Location permission granted via request!")
			# Stop the timer
			if permission_check_timer:
				permission_check_timer.queue_free()
				permission_check_timer = null

			auto_start_requested = true
			_update_status("READY")
		else:
			# Check if we've been waiting too long (timeout after 30 seconds)
			if permission_check_timer and permission_check_timer.wait_time * (30 / permission_check_timer.wait_time) <= 30:
				# Still waiting, continue checking
				return
			else:
				# Timeout - permission was likely denied
				Logger.error("GpsService", "Location permission denied or timed out.")
				if permission_check_timer:
					permission_check_timer.queue_free()
					permission_check_timer = null
				_update_status("ERROR_PERMISSION_DENIED")
				# Start periodic permission checking if denied
				_start_permission_check_timer()


func _check_permissions_periodically() -> void:
	# Only check if we're in an error state due to permissions
	if current_status == "ERROR_PERMISSION_DENIED" and OS.has_feature("android"):
		if OS.get_granted_permissions().has(PERMISSION_FINE_LOCATION):
			Logger.info("GpsService", "Location permission granted externally!")
			# Stop the timer
			if permission_check_timer:
				permission_check_timer.queue_free()
				permission_check_timer = null

			auto_start_requested = true
			_update_status("READY")
