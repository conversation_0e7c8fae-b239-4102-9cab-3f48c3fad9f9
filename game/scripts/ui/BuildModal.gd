extends "res://scripts/ui/Modal.gd" # Inherit from the base Modal script

# Signal for notifying about building placement result
signal building_placement_result(success: bool, message: String)

# References to specific nodes within this modal's content
@onready var building_options_container: Container = %BuildingOptionsContainer
@onready var building_name_input: LineEdit = %BuildingNameInput
@onready var building_description_label: Label = %BuildingDescriptionLabel
@onready var building_cost_container: VBoxContainer = %BuildingCostContainer
@onready var build_button: Button = %BuildButton
@onready var cancel_button: Button = %CancelButton

# Store building data
var available_building_types: Array = []
var selected_building_type: Dictionary = {}
var map_reference = null # Reference to the Map node

func _ready():
	super._ready() # Call the base class's ready function

	# Connect button signals
	if build_button:
		build_button.pressed.connect(_on_build_button_pressed)
	if cancel_button:
		cancel_button.pressed.connect(_on_cancel_button_pressed)

	# Connect API signals
	if KokumeApi:
		KokumeApi.building_types_loaded.connect(_on_building_types_loaded)
		KokumeApi.building_types_failed.connect(_on_building_types_failed)
		KokumeApi.building_created.connect(_on_building_created)
		KokumeApi.building_creation_failed.connect(_on_building_creation_failed)
	else:
		Logger.error("BuildModal", "KokumeApi autoload not found!")


# Called when the modal is opened with data
func open_and_fill_modal(data: Dictionary):
	# Store map reference for later use
	map_reference = data.get("Map")
	if not map_reference:
		Logger.error("BuildModal", "No map reference provided to build modal")
		close_modal()
		return

	# Reset UI state
	selected_building_type = {}
	building_name_input.text = ""
	building_description_label.text = ""
	_clear_cost_container()

	# Disable build button until a building type is selected
	build_button.disabled = true

	# Fetch available building types
	_fetch_building_types()

	# Set title
	get_node(^"MarginContainer/VBoxContainer/TitleLabel").text = "Build New Building"

	open_modal()


# Fetch available building types from the API
func _fetch_building_types():
	Logger.info("BuildModal", "Fetching building types from API")
	KokumeApi.get_building_types()


# API callback for building types loaded
func _on_building_types_loaded(data: Array):
	Logger.info("BuildModal", "Building types loaded from API: %d types" % data.size())
	available_building_types = data
	_populate_building_options()


# API callback for building types failed
func _on_building_types_failed(response_code: int, error_data: Dictionary):
	Logger.error("BuildModal", "Failed to load building types. Code: %d, Error: %s" % [response_code, error_data])


# Populate the building options container with available building types
func _populate_building_options():
	if not building_options_container:
		Logger.error("BuildModal", "BuildingOptionsContainer node not found!")
		return

	# Clear existing options
	for child in building_options_container.get_children():
		building_options_container.remove_child(child)
		child.queue_free()

	# Populate with available buildings
	if available_building_types.is_empty():
		var label = Label.new()
		label.text = "No buildings available to construct here."
		building_options_container.add_child(label)
		return

	for building_type in available_building_types:
		var button = Button.new()
		button.text = building_type.get("name", "Unknown")
		button.tooltip_text = building_type.get("description", "")
		button.custom_minimum_size = Vector2(0, 45) # Larger button for better touch control
		# Connect button press to signal the selection
		button.pressed.connect(_on_building_type_selected.bind(building_type))
		building_options_container.add_child(button)

		# Check if player has enough resources
		var can_afford = _check_can_afford_building(building_type)
		button.disabled = !can_afford


# Check if player can afford to build the selected building
func _check_can_afford_building(building_type: Dictionary) -> bool:
	var cost_array = building_type.get("cost", [])
	if cost_array.is_empty():
		return true

	for cost_item in cost_array:
		var resource_type = cost_item.get("type", {}).get("slug", "")
		var amount_needed = cost_item.get("amount", 0)

		# Find the player's resource amount
		var player_amount = 0
		for resource in GameState.player_resources:
			if resource.get("type", {}).get("slug", "") == resource_type:
				player_amount = resource.get("amount", 0)
				break

		if player_amount < amount_needed:
			return false

	return true

# Handle building type selection
func _on_building_type_selected(building_type: Dictionary):
	selected_building_type = building_type

	# Update UI with selected building info
	var description = building_type.get("description", "No description available.")
	# Limit description length to 150 characters
	if description.length() > 150:
		description = description.substr(0, 147) + "..."
	building_description_label.text = description

	# Set default building name
	var region_name = "Region"
	if GameState.nearby_territories.size() > 0:
		for territory in GameState.nearby_territories:
			if territory.get("id") == GameState.current_region_id:
				region_name = territory.get("name", "Region")
				break

	building_name_input.text = "%s - %s" % [building_type.get("name", "Building"), region_name]

	# Update cost display
	_update_cost_display(building_type)

	# Enable build button if player can afford it
	build_button.disabled = !_check_can_afford_building(building_type)

	# No need to switch tabs as we're using HBoxContainer

# Update the cost display for the selected building
func _update_cost_display(building_type: Dictionary):
	_clear_cost_container()

	var cost_array = building_type.get("cost", [])
	if cost_array.is_empty():
		var label = Label.new()
		label.text = "No resources required"
		building_cost_container.add_child(label)
		return

	for cost_item in cost_array:
		var resource_type = cost_item.get("type", {}).get("slug", "")
		var resource_name = cost_item.get("type", {}).get("name", resource_type)
		var amount_needed = cost_item.get("amount", 0)

		# Find the player's resource amount
		var player_amount = 0
		for resource in GameState.player_resources:
			if resource.get("type", {}).get("slug", "") == resource_type:
				player_amount = resource.get("amount", 0)
				break

		var label = Label.new()
		var text = "%s: %d" % [resource_name, amount_needed]

		# Show warning if not enough resources
		if player_amount < amount_needed:
			text += " (You have: %d)" % player_amount
			label.add_theme_color_override("font_color", Color.RED)
		else:
			text += " (You have: %d)" % player_amount
			label.add_theme_color_override("font_color", Color.GREEN)

		label.text = text
		building_cost_container.add_child(label)

# Clear the cost container
func _clear_cost_container():
	for child in building_cost_container.get_children():
		building_cost_container.remove_child(child)
		child.queue_free()

# Handle build button press
func _on_build_button_pressed():
	if selected_building_type.is_empty():
		return

	if building_name_input.text.strip_edges().is_empty():
		building_name_input.placeholder_text = "Please enter a name"
		return

	if not map_reference:
		Logger.error("BuildModal", "No map reference available")
		return

	# Check if player has enough resources
	if not _check_can_afford_building(selected_building_type):
		Logger.error("BuildModal", "Cannot afford building: %s" % selected_building_type.get("name", "Unknown"))
		building_placement_result.emit(false, "Not enough resources to build this structure")
		return

	# Get player position from map
	var player_pos = Vector2(map_reference.player_longitude, map_reference.player_latitude)

	# Create building data for API
	var building_data = {
		"type": selected_building_type.get("slug", ""),
		"name": building_name_input.text,
		"position": {
			"lat": player_pos.y,
			"lng": player_pos.x,
		},
		"options": {} # Additional options if needed
	}

	# Call API to create building
	if KokumeApi:
		Logger.info("BuildModal", "Calling API to create building: %s" % building_data)
		KokumeApi.create_building(building_data)

		# Don't close modal yet - wait for API response
		build_button.disabled = true
		build_button.text = "Building..."
	else:
		Logger.error("BuildModal", "KokumeApi not available for building placement")
		building_placement_result.emit(false, "API service not available")
		close_modal()

# Handle cancel button press
func _on_cancel_button_pressed():
	close_modal()

# This function is a duplicate and should be removed
# Use _check_can_afford_building instead

# Deduct building cost from player resources
func _deduct_building_cost(building_type: Dictionary):
	var cost_array = building_type.get("cost", [])
	if cost_array.is_empty():
		return

	# Create a copy of player resources to modify
	var updated_resources = []
	for resource in GameState.player_resources:
		updated_resources.append(resource.duplicate())

	# Deduct costs
	for cost_item in cost_array:
		var resource_type = cost_item.get("type", {}).get("slug", "")
		var amount_needed = cost_item.get("amount", 0)

		# Find and update the player's resource
		for resource in updated_resources:
			if resource.get("type", {}).get("slug", "") == resource_type:
				resource["amount"] = resource.get("amount", 0) - amount_needed
				break

	# Update GameState with new resources
	GameState.update_player_resources(updated_resources)

# API response handlers for building creation
func _on_building_created(response_data: Dictionary):
	Logger.info("BuildModal", "Building created successfully: %s" % response_data)

	# Deduct resources from player
	_deduct_building_cost(selected_building_type)

	# Emit success signal
	building_placement_result.emit(true, "Building constructed successfully")

	# Close modal
	close_modal()

	# Refresh map data by triggering a map update
	if map_reference and map_reference.has_method("refresh_map_data"):
		map_reference.refresh_map_data()


func _on_building_creation_failed(error_code: int, error_data: Dictionary):
	Logger.error("BuildModal", "Failed to create building. Code: %d, Error: %s" % [error_code, error_data])

	# Reset button state
	build_button.disabled = false
	build_button.text = "Build"

	# Show error message
	var error_message = error_data.get("message", "Unknown error")
	building_placement_result.emit(false, "Failed to construct building: %s" % error_message)
