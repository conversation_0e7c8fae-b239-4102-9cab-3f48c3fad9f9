<?php declare(strict_types = 1);

namespace App\Model\Api\Exception;

use Apitte\Core\Exception\Logical\InvalidArgumentException;
use Apitte\Core\Http\ApiResponse;
use Throwable;

class NotFoundException extends ApiException {

	public static string $defaultMessage = 'Resource not found';

	public function __construct(string $message = '', int $code = ApiResponse::S404_NOT_FOUND, ?Throwable $previous = null)
	{
		if ($code !== ApiResponse::S404_NOT_FOUND) {
			throw new InvalidArgumentException(sprintf('%s expects code to be %d, %d given', static::class, ApiResponse::S404_NOT_FOUND, $code));
		}

		parent::__construct('not_found', $message !== '' ? $message : static::$defaultMessage, $code, $previous);
	}

}