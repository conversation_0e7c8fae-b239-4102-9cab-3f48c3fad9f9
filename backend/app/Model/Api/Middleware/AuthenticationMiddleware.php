<?php declare(strict_types = 1);

namespace App\Model\Api\Middleware;

use App\Domain\User\User;
use App\Model\Api\RequestAttributes;
use App\Model\Database\EntityManager;
use App\Model\Security\SecurityUser;
use Contributte\Middlewares\IMiddleware;
use Contributte\Middlewares\Security\IAuthenticator;
use Exception;
use League\OAuth2\Server\Exception\OAuthServerException;
use Nette\Utils\Json;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;

class AuthenticationMiddleware implements IMiddleware {

	private const WHITELIST_PATHS = ['/api/v1/player-registration', '/api/oauth'];

	public function __construct(
		private readonly IAuthenticator $authenticator,
		private readonly SecurityUser 	$securityUser,
		private readonly EntityManager 	$em,
	) {}

	/**
	 * Authenticate user from given request
	 */
	public function __invoke(
		ServerRequestInterface $request,
		ResponseInterface $response,
		callable $next,
	) : ResponseInterface {
		// If request is in whitelist, then skip authentication
		if ($this->isWhitelisted($request)) {
			return $next($request, $response);
		}

		try {
			if ($this->securityUser->isLoggedIn()) {
				$user = $this->em->find(User::class, $this->securityUser->getIdentity()->getId());
			} else {
				$user = $this->authenticator->authenticate($request);
			}

			// If we have a identity, then go to next middlewares,
			// otherwise stop and return current response
			if ($user === NULL) {
				return $this->denied($request, $response);
			}

			// Add info about current logged user to request attributes
			$request = $request->withAttribute(RequestAttributes::APP_LOGGED_USER, $user);
		} catch (OAuthServerException $exception) {
			return $exception->generateHttpResponse($response);
			// @codeCoverageIgnoreStart
		} catch (Exception $exception) {
			return (new OAuthServerException($exception->getMessage(), 0, 'unknown_error', 500))
				->generateHttpResponse($response);
			// @codeCoverageIgnoreEnd
		}

		// Pass to next middleware
		return $next($request, $response);
	}

	protected function isWhitelisted(ServerRequestInterface $request) : bool {
		foreach (self::WHITELIST_PATHS as $whitelist) {
			if (str_starts_with($request->getUri()->getPath(), $whitelist)) {
				return TRUE;
			}
		}

		return FALSE;
	}

	protected function denied(ServerRequestInterface $request, ResponseInterface $response) : ResponseInterface {
		$response->getBody()->write(Json::encode([
			'status'  => 'error',
			'message' => 'Client authentication failed',
			'code'    => 401,
		]));

		return $response
			->withHeader('Content-Type', 'application/json')
			->withStatus(401);
	}

}
