<?php declare(strict_types = 1);

namespace App\Model\Utils;

use Nette\Utils\Random as NetteRandom;

class Random {

	public static function generate(int $length = 10, string $charlist = '0-9a-z') : string {
		return NetteRandom::generate($length, $charlist);
	}

	public static function generateIdentifier(int $length = 100) : string {
		if ($length < 23) {
			throw new \InvalidArgumentException('Length must be greater or equal to 23.');
		}

		if ($length > 23) {
			return uniqid(self::generate($length - 23), TRUE);
		}

		return uniqid('', TRUE);
	}

}