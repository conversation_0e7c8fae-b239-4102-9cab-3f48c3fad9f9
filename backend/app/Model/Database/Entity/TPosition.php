<?php declare(strict_types = 1);

namespace App\Model\Database\Entity;

use App\Model\Utils\Position;
use Doctrine\ORM\Mapping as ORM;
use Jsor\Doctrine\PostGIS\Types\PostGISType;

trait TPosition {

	#[ORM\Column(
		type: PostGISType::GEOMETRY,
		options: ['geometry_type' => 'POINT', 'srid' => 4326],
	)]
	protected string $position;

	public function getPosition() : Position {
		[$lng, $lat] = \sscanf($this->position, 'SRID=4326;POINT(%f %f)');
		return Position::create($lng, $lat);
	}

	public function setPosition(Position $position) : self {
		$this->position = \sprintf('SRID=4326;POINT(%F %F)', $position->getLng(), $position->getLat());
		return $this;
	}

}
