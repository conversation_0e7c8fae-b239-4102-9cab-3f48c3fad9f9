<?php declare(strict_types = 1);

namespace App\API\V1\Building;

use Apitte\Core\Annotation\Controller as Apitte;
use Apitte\Core\Http\ApiRequest;
use App\API\V1\BaseV1Controller;
use App\Domain\Api\Facade\BuildingFacade;
use App\Domain\Api\Request\CreateBuildingReqDto; // Správný namespace pro CreateBuildingReqDto
use App\Domain\Api\Request\CreateUnitReqDto;
use App\Domain\Api\Response\BuildingResDto;
use App\Domain\Api\Response\BuildingStealResDto;
use App\Domain\Api\Response\UnitResDto;
use App\Domain\Building\Facade\BuildingDemolishFacade;
use App\Domain\Building\Facade\BuildingUpgradeFacade;
use App\Domain\Building\Service\BuildingStealService;
use App\Domain\Combat\CombatService;
use App\Domain\Service\PickUpResourcesService;
use App\Model\Api\Exception\ApiException;
use App\Model\Utils\Caster;

/**
 * @Apitte\Path("/building")
 * @Apitte\Id("building")
 */
class BuildingController extends BaseV1Controller {

	public function __construct(
		private readonly BuildingFacade 		$buildingFacade,
		private readonly PickUpResourcesService $pickUpResourcesService,
		private readonly BuildingUpgradeFacade 	$buildingUpgradeFacade,
		private readonly BuildingDemolishFacade $buildingDemolishFacade,
        private readonly BuildingStealService 	$buildingStealService,
	) {}

	/**
	 * @Apitte\OpenApi("
	 * 		summary: Find building by ID
	 * ")
	 * @Apitte\Path("/{buildingId}")
	 * @Apitte\Method("GET")
	 * @Apitte\RequestParameters({
	 *      @Apitte\RequestParameter(name="buildingId", type="string", description="Building ID", in="path", required=true),
	 *  })
	 */
	public function get(ApiRequest $request) : BuildingResDto {
		$player = $this->getPlayer($request);
		$buildingId = Caster::toString($request->getParameter('buildingId'));
		return BuildingResDto::from($this->buildingFacade->getOneForPlayer($buildingId, $player)); // Stále vrací DTO
	}

	/**
	 * @Apitte\OpenApi("
	 * 		summary: List of buildings
	 * ")
	 * @Apitte\Path("/list")
	 * @Apitte\Method("GET")
	 *
	 * @return array<BuildingResDto>
	 */
	public function list(ApiRequest $request) : array {
		$player = $this->getPlayer($request);
		return $this->buildingFacade->findAllByPlayer($player);
	}

	/**
	 * @Apitte\OpenApi("
	 * 		summary: Build new building
	 * ")
	 * @Apitte\Path("/")
	 * @Apitte\Method("PUT")
	 * @Apitte\RequestBody(entity=CreateBuildingReqDto::class, description="New building data", required=true, validation=true),
	 */
	public function new(ApiRequest $request) : BuildingResDto {
		$player = $this->getPlayer($request);

		/** @var CreateBuildingReqDto $dto */
		$dto = $request->getParsedBody();

		return $this->buildingFacade->createBuildingOnPlayerPos($player, $dto);
	}

	/**
	 * @Apitte\OpenApi("
	 * 		summary: Pick up resources from building
	 * ")
	 * @Apitte\Path("/{buildingId}/pick-up-resources")
	 * @Apitte\Method("GET")
	 * @Apitte\RequestParameters({
	 *     @Apitte\RequestParameter(name="buildingId", type="string", description="Building ID", in="path", required=true),
	 * })
	 */
	public function pickUpResources(ApiRequest $request) : BuildingResDto {
		$player = $this->getPlayer($request);
		$buildingId = Caster::toString($request->getParameter('buildingId'));

		return BuildingResDto::from($this->pickUpResourcesService->pickUpResources($player, $buildingId));
	}

	/**
	 * @Apitte\OpenApi("
	 * 		summary: Upgrade building
	 * ")
	 * @Apitte\Path("/{buildingId}/upgrade")
	 * @Apitte\Method("GET")
	 * @Apitte\RequestParameters({
	 *     @Apitte\RequestParameter(name="buildingId", type="string", description="Building ID", in="path", required=true),
	 * })
	 */
	public function upgrade(ApiRequest $request) : BuildingResDto {
		$player = $this->getPlayer($request);
		$buildingId = Caster::toString($request->getParameter('buildingId'));

		return BuildingResDto::from($this->buildingUpgradeFacade->upgrade($player, $buildingId));
	}

	/**
	 * @Apitte\OpenApi("
	 * 		summary: Demolish building
	 * ")
	 * @Apitte\Path("/{buildingId}/demolish")
	 * @Apitte\Method("GET")
	 * @Apitte\RequestParameters({
	 *     @Apitte\RequestParameter(name="buildingId", type="string", description="Building ID", in="path", required=true),
	 * })
	 */
	public function demolish(ApiRequest $request) : BuildingResDto {
		$player = $this->getPlayer($request);
		$buildingId = Caster::toString($request->getParameter('buildingId'));

		return BuildingResDto::from($this->buildingDemolishFacade->demolish($player, $buildingId));
	}

	/**
	 * @Apitte\OpenApi("
	 * 		summary: Steal resources from building
	 * ")
	 * @Apitte\Path("/{buildingId}/steal")
	 * @Apitte\Method("GET")
	 * @Apitte\RequestParameters({
	 *     @Apitte\RequestParameter(name="buildingId", type="string", description="Building ID", in="path", required=true),
	 * })
	 */
	public function steal(ApiRequest $request) : BuildingStealResDto {
		$player = $this->getPlayer($request);
		$buildingId = Caster::toString($request->getParameter('buildingId'));

		$hero = $player->getHero();
		if ($hero === null) {
			throw new ApiException('Player has no hero');
		}

		$building = $this->buildingFacade->getOneForPlayer($buildingId, $player);

		['combatResult' => $combatResult, 'loot' => $loot] = $this->buildingStealService->stealResources($building, $hero);

		return BuildingStealResDto::from($building, $combatResult, $loot);
	}

	/**
	 * @Apitte\OpenApi("
	 * summary: Produce unit
	 * ")
	 * @Apitte\Path("/produce")
	 * @Apitte\Method("PUT")
	 * @Apitte\RequestBody(entity=CreateUnitReqDto::class, description="Unit production data", required=true, validation=true)
	 */
	public function produce(ApiRequest $request) : UnitResDto {
		$player = $this->getPlayer($request);

		/** @var CreateUnitReqDto $dto */
		$dto = $request->getParsedBody();

		return $this->unitFacade->produceUnits($player, $dto);
	}

}