<?php declare(strict_types = 1);

namespace App\Domain\Region;

enum RegionLevel : string {

	case lvl1 = '1';
	case lvl2 = '2';
	case lvl3 = '3';
	case lvl4 = '4';
	case lvl5 = '5'; // Base level
	case lvl6 = '6'; // Resources

	private const NAMES = [
		self::lvl1->value => 'Level 1',
		self::lvl2->value => 'Level 2',
		self::lvl3->value => 'Level 3',
		self::lvl4->value => 'Level 4',
		self::lvl5->value => 'Level 5',
		self::lvl5->value => 'Resources',
	];

	public function getName() : string {
		return self::NAMES[$this->value];
	}

}
