<?php declare(strict_types = 1);

namespace App\Domain\Region;

use App\Domain\Resource\ResourceType;
use App\Model\Utils\Caster;
use App\Model\Utils\Position;

class CreateRegionFacade {

	/**
	 * @param array{osm_id: string, name: string, admin_level:string|null, position:string} $data
	 */
	public function createRegion(array $data) : Region {
		$regionLevel = match ($data['admin_level']) {
			'2' 		=> RegionLevel::lvl2,
			'6' 		=> RegionLevel::lvl3,
			'7' 		=> RegionLevel::lvl4,
			'9', '10' 	=> RegionLevel::lvl5,
			NULL 		=> RegionLevel::lvl6,
		};

		return new Region(
			Caster::toString($data['name']),
			Caster::toInt($data['osm_id']),
			$regionLevel,
			$this->parsePoint($data['position'])
		);
	}

	/**
	 * @param array{osm_id: string, name: string|null, position:string, landuse: string|null, natural:string|null, area_m2:float} $data
	 */
	public function createResourceRegion(ResourceRegionType $resourceRegionType, array $data) : ResourceRegion {
		$resourceRegion = new ResourceRegion(Caster::toInt($data['osm_id']), RegionLevel::lvl6, $this->parsePoint($data['position']), $resourceRegionType);

		if ($data['name'] !== NULL) {
			$resourceRegion->setName(Caster::toString($data['name']));
		}

		$this->fillResourceRegion($resourceRegion, $data);

		return $resourceRegion;
	}

	public function fillResourceRegion(ResourceRegion $resourceRegion, array $data) : void {
		$areaM2 = Caster::toFloat($data['area_m2']);

		// set amount of resources by region type
		$amountBase = rand((int)($areaM2 * 0.7), (int)($areaM2 * 2));
		foreach ($resourceRegion->getResourceRegionType()->getResources() as $resourceType => $amount) {
			$resourceRegion->setResourceAmount(
				ResourceType::from($resourceType),
				(int)round($amountBase * $amount),
			);
		}
	}

	/**
	 * @param string $point "POINT(lng lat)"
	 */
	private function parsePoint(string $point) : Position {
		preg_match('/POINT\(([^ ]+) ([^ ]+)\)/', $point, $matches);
		return new Position((float)$matches[1], (float)$matches[2]);
	}

}