<?php declare(strict_types = 1);

namespace App\Domain\OAuth2\AccessToken;

use App\Domain\OAuth2\Client\Client;
use App\Domain\OAuth2\Scope\Scope;
use App\Model\Database\Entity\AbstractEntity;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;
use League\OAuth2\Server\Entities\AccessTokenEntityInterface;
use League\OAuth2\Server\Entities\ScopeEntityInterface;
use League\OAuth2\Server\Entities\Traits\AccessTokenTrait;
use League\OAuth2\Server\Entities\Traits\EntityTrait;
use League\OAuth2\Server\Entities\Traits\TokenEntityTrait;

#[ORM\Entity(repositoryClass: AccessTokenRepository::class)]
#[ORM\Table(name: 'api_access_token')]
class AccessToken extends AbstractEntity implements AccessTokenEntityInterface {

	use EntityTrait;
	use AccessTokenTrait;
	use TokenEntityTrait;

	#[ORM\Id]
	#[ORM\Column(type: 'string', nullable: false)]
	protected $identifier;

	/**
	 * @var string[]
	 */
	#[ORM\Column(type: 'json', nullable: false)]
	protected $scopes = [];

	/**
	 * @var DateTimeImmutable
	 */
	#[ORM\Column(type: 'datetime_immutable', nullable: false)]
	protected $expiryDateTime;

	/**
	 * @var string|int|null
	 */
	#[ORM\Column(type: 'string', nullable: true)]
	protected $userIdentifier;

	/**
	 * @var Client
	 */
	#[ORM\ManyToOne(targetEntity: Client::class)]
	#[ORM\JoinColumn(name: 'client_id', referencedColumnName: 'identifier', nullable: false, onDelete: 'CASCADE')]
	protected $client;

	public function __construct(Client $clientEntity, array $scopes, $userIdentifier = NULL) {
		$this->client = $clientEntity;
		$this->scopes = array_map(fn(Scope $scope) => $scope->getIdentifier(), $scopes);
		$this->userIdentifier = $userIdentifier;
	}

	/**
	 * @param Scope $scope
	 */
	public function addScope(ScopeEntityInterface $scope) : void {
		$this->scopes[] = $scope->getIdentifier();
	}

	/**
	 * @return Scope[]
	 */
	public function getScopes() : array {
		return array_map(fn($scope) => new Scope($scope), $this->scopes);
	}

}