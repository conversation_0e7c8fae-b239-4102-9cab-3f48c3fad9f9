<?php declare(strict_types = 1);

namespace App\Domain\OAuth2\RefreshToken;

use App\Model\Database\Repository\AbstractRepository;
use League\OAuth2\Server\Entities\RefreshTokenEntityInterface;
use League\OAuth2\Server\Repositories\RefreshTokenRepositoryInterface;

/**
 * @extends AbstractRepository<RefreshToken>
 * @method RefreshToken|null find($id, ?int $lockMode = NULL, ?int $lockVersion = NULL)
 * @method RefreshToken|null findOneBy(array $criteria, ?array $orderBy = NULL)
 * @method RefreshToken[] findAll()
 * @method RefreshToken[] findBy(array $criteria, ?array $orderBy = NULL, ?int $limit = NULL, ?int $offset = NULL)
 */
class RefreshTokenRepository extends AbstractRepository implements RefreshTokenRepositoryInterface {

	/**
	 * @inheritDoc
	 */
	public function getNewRefreshToken() : RefreshToken {
		return new RefreshToken();
	}

	/**
	 * @inheritDoc
	 */
	public function persistNewRefreshToken(RefreshTokenEntityInterface $refreshTokenEntity) : void {
		$this->getEntityManager()->persist($refreshTokenEntity);
		$this->getEntityManager()->flush();
	}

	/**
	 * @inheritDoc
	 */
	public function revokeRefreshToken($tokenId) : void {
		$refreshToken = $this->find($tokenId);
		if (!$refreshToken) {
			return;
		}

		$this->getEntityManager()->remove($refreshToken);
	}

	/**
	 * @inheritDoc
	 */
	public function isRefreshTokenRevoked($tokenId) : bool {
		return $this->find($tokenId) === NULL;
	}

}