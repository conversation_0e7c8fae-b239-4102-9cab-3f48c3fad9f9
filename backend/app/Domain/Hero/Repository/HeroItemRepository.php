<?php declare(strict_types = 1);

namespace App\Domain\Hero\Repository;

use App\Domain\Hero\HeroItem;
use App\Model\Database\Repository\AbstractRepository;

/**
 * @method HeroItem|null find($id, ?int $lockMode = null, ?int $lockVersion = null)
 * @method HeroItem|null findOneBy(array $criteria, array $orderBy = null)
 * @method HeroItem[] findAll()
 * @method HeroItem[] findBy(array $criteria, array $orderBy = null, ?int $limit = null, ?int $offset = null)
 */
class HeroItemRepository extends AbstractRepository {

}