<?php declare(strict_types = 1);

namespace App\Domain\Hero;

use App\Model\Database\Repository\AbstractRepository;

/**
 * @method Hero|NULL find($id, ?int $lockMode = NULL, ?int $lockVersion = NULL)
 * @method Hero|NULL findOneBy(array $criteria, array $orderBy = NULL)
 * @method Hero[] findAll()
 * @method Hero[] findBy(array $criteria, array $orderBy = NULL, ?int $limit = NULL, ?int $offset = NULL)
 *
 * @extends AbstractRepository<Hero>
 */
class HeroRepository extends AbstractRepository {

}