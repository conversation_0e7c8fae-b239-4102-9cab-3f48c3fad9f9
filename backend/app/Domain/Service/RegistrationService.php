<?php declare(strict_types = 1);

namespace App\Domain\Service;

use App\Domain\Api\Request\RegistrationReqDto;
use App\Domain\Player\CreatePlayerFacade;
use App\Domain\Player\Player;
use App\Domain\Player\PlayerQuery;
use App\Domain\User\CreateUserFacade;
use App\Domain\User\Exception\UserExistsException;
use App\Domain\User\UserQuery;
use App\Model\Database\QueryManager;

class RegistrationService {

	public function __construct(
		private readonly QueryManager 		$qm,
		private readonly CreateUserFacade 	$createUserFacade,
		private readonly CreatePlayerFacade $createPlayerFacade,
	) {}

	public function registerPlayer(RegistrationReqDto $dto) : Player {
		if ($this->qm->findOne(UserQuery::ofEmail($dto->email))) {
			throw UserExistsException::fromEmail($dto->email);
		}

		if ($this->qm->findOne(PlayerQuery::ofNickname($dto->nickname))) {
			throw UserExistsException::fromNickname($dto->nickname);
		}

		$user = $this->createUserFacade->createUser($dto->email, $dto->password);
		return $this->createPlayerFacade->createPlayer($user, $dto->nickname);
	}

}