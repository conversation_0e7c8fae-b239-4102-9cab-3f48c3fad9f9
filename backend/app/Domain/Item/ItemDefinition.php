<?php declare(strict_types = 1);

namespace App\Domain\Item;

use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TCreatedAt;
use App\Model\Database\Entity\TId;
use App\Model\Database\Entity\TUpdatedAt;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ItemDefinitionRepository::class)]
#[ORM\Table(name: 'game_item_definition')]
#[ORM\HasLifecycleCallbacks]
class ItemDefinition extends AbstractEntity {

	use TId;
	use TCreatedAt;
	use TUpdatedAt;

	#[ORM\Column(type: 'string', enumType: ItemType::class)]
	private ItemType $type;

	#[ORM\Column(type: 'string', enumType: ItemCategory::class)]
	private ItemCategory $category;

	#[ORM\Column(type: 'string')]
	private string $name;

	#[ORM\Column(type: 'text')]
	private string $description;

	/**
	 * Can the item be spawned in the world?
	 */
	#[ORM\Column(type: 'boolean', options: ['default' => false])]
	private bool $spawnable = false;

	/**
	 * Commonness of the item in the world
	 *
	 * @var int<0,100>
	 */
	#[ORM\Column(type: 'integer', options: ['default' => 0])]
	private int $commonness = 0;

	#[ORM\Column(nullable: true)]
	private ?int $minLevel = NULL;

	/**
	 * @var array<string, int>
	 */
	#[ORM\Column(type: 'json', options: ['default' => '{}'])]
	private array $attributes = [];


	public function __construct(ItemType $type, ItemCategory $category, string $name, string $description) {
		$this->type = $type;
		$this->category = $category;
		$this->name = $name;
		$this->description = $description;
	}

	public function getType() : ItemType {
		return $this->type;
	}

	public function setType(ItemType $type) : void {
		$this->type = $type;
	}

	public function getCategory() : ItemCategory {
		return $this->category;
	}

	public function setCategory(ItemCategory $category) : void {
		$this->category = $category;
	}

	public function getName() : string {
		return $this->name;
	}

	public function setName(string $name) : void {
		$this->name = $name;
	}

	public function getDescription() : string {
		return $this->description;
	}

	public function setDescription(string $description) : void {
		$this->description = $description;
	}

	public function isSpawnable() : bool {
		return $this->spawnable;
	}

	public function setSpawnable(bool $spawnable) : void {
		$this->spawnable = $spawnable;
	}

	/**
	 * @return int<0,100>
	 */
	public function getCommonness() : int {
		return $this->commonness;
	}

	/**
	 * @param int<0,100> $commonness
	 */
	public function setCommonness(int $commonness) : void {
		$this->commonness = $commonness;
	}

	public function createItem() : Item {
		return new Item($this);
	}

	public function getMinLevel() : ?int {
		return $this->minLevel;
	}

	public function setMinLevel(?int $minLevel) : void {
		$this->minLevel = $minLevel;
	}

	public function getAttributes() : array {
		return $this->attributes;
	}

	public function hasAttribute(string $key) : bool {
		return isset($this->attributes[$key]);
	}

	/**
	 * @param array<string, int> $attributes
	 */
	public function setAttributes(array $attributes) : void {
		$this->attributes = $attributes;
	}

	public function addAttribute(string $key, int $value) : void {
		$this->attributes[$key] = $value;
	}

	public function removeAttribute(string $key) : void {
		unset($this->attributes[$key]);
	}

	public function clearAttributes() : void {
		$this->attributes = [];
	}

}
