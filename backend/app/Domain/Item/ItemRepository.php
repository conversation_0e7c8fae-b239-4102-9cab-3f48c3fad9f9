<?php declare(strict_types = 1);

namespace App\Domain\Item;

use App\Model\Database\Repository\AbstractRepository;

/**
 * @extends AbstractRepository<Item>
 * @method Item|null find($id, ?int $lockMode = null, ?int $lockVersion = null)
 * @method Item|null findOneBy(array $criteria, array $orderBy = null)
 * @method Item[] findAll()
 * @method Item[] findBy(array $criteria, array $orderBy = null, ?int $limit = null, ?int $offset = null)
 */
class ItemRepository extends AbstractRepository {

}