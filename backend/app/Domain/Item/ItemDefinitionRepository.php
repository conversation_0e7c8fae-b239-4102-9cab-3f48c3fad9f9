<?php declare(strict_types = 1);

namespace App\Domain\Item;

use App\Model\Database\Repository\AbstractRepository;
use Random\RandomException;
use <PERSON>\Debugger;
use <PERSON>\ILogger;

/**
 * @extends AbstractRepository<ItemDefinition>
 * @method ItemDefinition|null find($id, ?int $lockMode = null, ?int $lockVersion = null)
 * @method ItemDefinition|null findOneBy(array $criteria, array $orderBy = null)
 * @method ItemDefinition[] findAll()
 * @method ItemDefinition[] findBy(array $criteria, array $orderBy = null, ?int $limit = null, ?int $offset = null)
 */
class ItemDefinitionRepository extends AbstractRepository {

	/**
	 * Find spawnable random item, items with higher commonness have higher chance to be selected
	 * @return ItemDefinition|null
	 */
	public function findSpawnableRandomItem() : ?ItemDefinition {
		/** @var array<int, array{commonness: int, id:int}> $items */
		$items = $this->createQueryBuilder('i', 'i.id')
			->select(['i.commonness', 'i.id'])
			->where('i.spawnable = true')
			->getQuery()
			->getResult();

		$totalWeight = array_sum(array_map(fn($item) => $item['commonness'], $items));

		if ($totalWeight === 0) {
			return null;
		}

		try {
			$randomWeight = random_int(1, $totalWeight);
		} catch (RandomException $ex) {
			Debugger::log($ex, ILogger::CRITICAL);
			return null;
		}

		$currentWeight = 0;
		foreach ($items as $item) {
			$currentWeight += $item['commonness'];
			if ($currentWeight >= $randomWeight) {
				return $this->find($item['id']);
			}
		}

		return null;
	}

}