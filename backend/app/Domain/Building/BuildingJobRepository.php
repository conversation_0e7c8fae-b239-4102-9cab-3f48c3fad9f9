<?php declare(strict_types = 1);

namespace App\Domain\Building;

use App\Model\Database\Repository\AbstractRepository;

/**
 * @extends AbstractRepository<BuildingJob>
 *
 * @method BuildingJob|null find(int $id)
 * @method BuildingJob[] findAll()
 * @method BuildingJob[] findBy(array $criteria, array $orderBy = null, int $limit = null, int $offset = null)
 * @method BuildingJob|null findOneBy(array $criteria, array $orderBy = null)
 */
class BuildingJobRepository extends AbstractRepository {

}