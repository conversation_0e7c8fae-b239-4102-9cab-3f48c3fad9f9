<?php declare(strict_types = 1);

namespace App\Domain\Building;

use App\Domain\Region\RegionLevel;
use App\Model\Database\Repository\AbstractRepository;
use App\Model\Utils\Position;
use Doctrine\ORM\Query\ResultSetMappingBuilder;

/**
 * @method Building|null find(string $id)
 * @method Building|null findOneBy(array $criteria)
 * @method Building[] findAll()
 * @method Building[] findBy(array $criteria)
 *
 * @extends AbstractRepository<Building>
 */
class BuildingRepository extends AbstractRepository {

	/**
	 * @return Building[]
	 */
	public function findToStateUpdate(int $limit, int $offset) : array {
		return $this->createQueryBuilder('b')
					->where('b.stateValidTo <= :now')
					->setParameter('now', new \DateTime())
					->setMaxResults($limit)
					->setFirstResult($offset)
					->getQuery()
					->getResult();
	}

	/**
	 * @return Building[]
	 * @throws \DateMalformedStringException
	 */
	public function findToResourcesUpdate(int $limit, int $offset, string $period = '1 minute') : array {
		return $this->createQueryBuilder('b')
					->where('b.lastResourcesUpdate IS NULL OR b.lastResourcesUpdate <= :now')
					->setParameter('now', new \DateTime('-' . $period))
					->setMaxResults($limit)
					->setFirstResult($offset)
					->getQuery()
					->getResult();
	}

	/**
	 * @return Building[]
	 */
	public function findInRadius(Position $position, int $range) : array {
		$rsm = new ResultSetMappingBuilder($this->_em);
		$rsm->addRootEntityFromClassMetadata(Building::class, 'gb');

		return $this
			->_em
			->createNativeQuery(
			// params is used to calculate the range in degrees
			// 111000 is the length of 1 degree in meters
			// 111000 * COS(RADIANS(:lat)) is the length of 1 degree in meters on the longitude
			// ST_MakeEnvelope is used to filter the results by the bounding box
			// ST_DWithin is used to filter the results by the range
				'
					WITH params AS (
						SELECT 
							CAST(:range AS FLOAT) / 111000 AS latitude_deg,
							CAST(:range AS FLOAT) / (111000 * COS(RADIANS(:lat))) AS longitude_deg
					)
					SELECT gb.*, ST_AsEWKT(gb.position) AS position 
					FROM game_building gb
					CROSS JOIN params
					WHERE 
						gb.position && ST_MakeEnvelope(
							:lng - params.longitude_deg, :lat - params.latitude_deg,
							:lng + params.longitude_deg, :lat + params.latitude_deg,
							4326
						) 
						AND ST_DWithin(
							gb.position,
							ST_SetSRID(ST_MakePoint(:lng, :lat), 4326),
							GREATEST(params.latitude_deg, params.longitude_deg)
						);
        		',
				$rsm,
			)
			->setParameter('lng', $position->getLng())
			->setParameter('lat', $position->getLat())
			->setParameter('range', $range)
			->setParameter('regionLevel', RegionLevel::lvl6)
			->getResult();
	}

}