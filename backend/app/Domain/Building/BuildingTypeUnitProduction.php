<?php declare(strict_types = 1);

namespace App\Domain\Building;

use App\Domain\Resource\ResourceType;
use App\Domain\Unit\UnitDefinition;
use App\Model\Database\Entity\AbstractEntity;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'game_building_type_unit_production')]
class BuildingTypeUnitProduction extends AbstractEntity {

	#[ORM\Id]
	#[ORM\ManyToOne(targetEntity: BuildingType::class)]
	#[ORM\JoinColumn(name: 'building_type_id', referencedColumnName: 'id', nullable: FALSE, onDelete: 'CASCADE')]
	private BuildingType $buildingType;

	#[ORM\Id]
	#[ORM\ManyToOne(targetEntity: UnitDefinition::class)]
	#[ORM\JoinColumn(name: 'unit_definition_id', referencedColumnName: 'id', nullable: FALSE, onDelete: 'CASCADE')]
	private UnitDefinition $unitDefinition;

	/** @var int Production time in minutes */
	#[ORM\Column(type: 'integer', nullable: false)]
	private int $productionTime;

	/** @var array<ResourceType::*, int> */
	#[ORM\Column(type: 'json', nullable: false, options: ['default' => '{}'])]
	private array $cost = [];

	/**
	 * @param array<ResourceType::*, int> $cost
	 */
	public function __construct(BuildingType $buildingType, UnitDefinition $unitDefinition, int $productionTime, array $cost) {
		$this->buildingType = $buildingType;
		$this->unitDefinition = $unitDefinition;
		$this->productionTime = $productionTime;
		$this->cost = $cost;
	}

	public function getBuildingType() : BuildingType {
		return $this->buildingType;
	}

	public function getUnitDefinition() : UnitDefinition {
		return $this->unitDefinition;
	}

	/**
	 * @return int Production time in minutes
	 */
	public function getProductionTime() : int {
		return $this->productionTime;
	}

	public function setProductionTime(int $productionTime) : void {
		$this->productionTime = $productionTime;
	}
	
	/**
	 * @return array{type: ResourceType, amount: int}[]
	 */
	public function getCost() : array {
		return array_map(
			fn(string $resource, int $amount) => [
				'type' => ResourceType::from($resource),
				'amount' => $amount,
			],
			array_keys($this->cost),
			array_values($this->cost),
		);
	}

	public function setCost(ResourceType $type, int $cost) : void {
		$this->cost[$type->value] = $cost;
	}

	public function removeCost(ResourceType $type) : void {
		unset($this->cost[$type->value]);
	}

	public function clearCost() : void {
		$this->cost = [];
	}
}