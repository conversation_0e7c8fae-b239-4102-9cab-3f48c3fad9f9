<?php declare(strict_types = 1);

namespace App\Domain\Building\Facade;

use App\Domain\Building\Building;
use App\Domain\Building\BuildingResource;
use App\Model\Database\EntityManager;

class BuildingProductionFacade {

	public function __construct(
		private readonly EntityManager $em,
	) {}

	/**
	 * @param Building[] $buildings
	 */
	public function updateProductions(array $buildings) : void {
		foreach ($buildings as $building) {
			$this->updateProduction($building, FALSE);
		}

		$this->em->flush();
	}

	public function updateProduction(Building $building, bool $flush = TRUE) : void {
		$buildingType = $building->getType();

		if (!$buildingType->hasProduction()) {
			return;
		}

		$productions = $buildingType->getProductions();
		$buildingResources = $building->getResources();

		foreach ($productions as $production) {

			$resourceType = $production['type'];

			// Find resource regions with resource type
			$resources = $buildingResources->filter(fn(BuildingResource $br) => in_array($resourceType, $br->getResourceRegion()->getResourceTypes()));
			if ($resources->count() === 0) {
				continue;
			}

			// Calculate production amount
			$accumulator = $building->getProductionAccumulator($resourceType);
			$hourlyProduction = $production['levels'][$building->getLevel()] ?? 0;
			$minuteProduction = $hourlyProduction / 60;

			$accumulator += $minuteProduction;

			// Get integer part of accumulated production
			$productionAmount = (int) floor($accumulator);

			// Decrease accumulator by integer part
			$accumulator -= $productionAmount;

			foreach ($resources as $resource) {
				$resAmount = $resource->getResourceRegion()->getResourceAmount($resourceType);

				// Move resources from region to building
				$amountToMove = min($resAmount->getAmount(), $productionAmount);

				$resAmount->decreaseAmount($amountToMove);
				$building->increaseResourceStock($resourceType, $amountToMove);
				$resource->increaseTotalProduction($resourceType, $amountToMove);


				// Stop if production amount is reached
				if ($resAmount->getAmount() >= $productionAmount) {
					break;
				}

				$productionAmount -= $resAmount->getAmount();
			}

			$building->setProductionAccumulator($resourceType, $accumulator);
		}

		$building->setLastResourcesUpdate();

		if ($flush) {
			$this->em->flush();
		}
	}

}