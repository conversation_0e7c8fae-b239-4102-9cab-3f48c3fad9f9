<?php declare(strict_types = 1);

namespace App\Domain\Building;

use App\Domain\Region\ResourceRegion;
use App\Domain\Resource\ResourceType;
use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TId;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'game_building_resource')]
class BuildingResource extends AbstractEntity {

	use TId;

	#[ORM\ManyToOne(targetEntity: Building::class, inversedBy: 'resources')]
	#[ORM\JoinColumn(name: 'building_id', referencedColumnName: 'id', nullable: false, onDelete: 'CASCADE')]
	private Building $building;

	#[ORM\ManyToOne(targetEntity: ResourceRegion::class)]
	#[ORM\JoinColumn(name: 'resource_region_id', referencedColumnName: 'id', nullable: false, onDelete: 'CASCADE')]
	private ResourceRegion $resourceRegion;

	#[ORM\Column(type: 'float', nullable: false)]
	private float $distance;

	#[ORM\Column(type: 'json', nullable: false, options: ['default' => '{}'])]
	private array $totalProduction = [];

	public function __construct(Building $building, ResourceRegion $resourceRegion, float $distance) {
		$this->building = $building;
		$this->resourceRegion = $resourceRegion;
		$this->distance = $distance;
	}

	public function getBuilding() : Building {
		return $this->building;
	}

	public function getResourceRegion() : ResourceRegion {
		return $this->resourceRegion;
	}

	public function getDistance() : float {
		return $this->distance;
	}

	/**
	 * @return array<string, int>
	 */
	public function getTotalProduction() : array {
		return $this->totalProduction;
	}

	public function increaseTotalProduction(ResourceType $resource, int $amount) : void {
		if (!isset($this->totalProduction[$resource->value])) {
			$this->totalProduction[$resource->value] = 0;
		}

		$this->totalProduction[$resource->value] += $amount;
	}

}