<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Building\Building;
use App\Domain\Player\Player;
use App\Domain\Region\Region;

class HearthBeatResDto extends ResDto {

	public CurrentPlayerResDto $player;
	public RegionResDto $region;

	/** @var BuildingResDto[] */
	public array $currentRegionBuildings = [];

	public static function from(Player $player, Region $region, array $buildings) : self {
		$self = new self();
		$self->player = CurrentPlayerResDto::from($player);
		$self->region = RegionResDto::from($region);
		$self->currentRegionBuildings = array_map(fn(Building $building) => BuildingResDto::from($building), $buildings);

		return $self;
	}

}