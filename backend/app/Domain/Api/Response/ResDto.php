<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use Apitte\Core\Mapping\Response\BasicEntity;

abstract class ResDto extends BasicEntity {

	public function toArray() : array {
		$data = [];
		$properties = $this->getProperties();

		foreach ($properties as $property) {
			if (!isset($this->{$property['name']})) {
				continue;
			}

			// Convert camelCase to snake_case
			$propertyName = strtolower(preg_replace('/[A-Z]/', '_\\0', lcfirst($property['name'])));

			$data[$propertyName] = $this->{$property['name']};
		}

		return $data;
	}

}