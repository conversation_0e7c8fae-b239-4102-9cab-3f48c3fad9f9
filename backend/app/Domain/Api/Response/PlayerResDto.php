<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Player\Player;

class PlayerResDto extends ResDto {

	public int $id;
	public string $nickname;
	public int $range;

	public ?HeroResDto $hero;

	public static function from(Player $player) : self {
		$self = new self();
		$self->id = $player->getId();
		$self->nickname = $player->getNickname();
		$self->range = $player->getRange();

		$self->hero = $player->getHero() ? HeroResDto::from($player->getHero()) : NULL;

		return $self;
	}

}