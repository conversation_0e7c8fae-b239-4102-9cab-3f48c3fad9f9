<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Resource\ResourceType;

class PlayerResourceResDto extends ResDto {

	/** @var ResourceTypeResDto[] */
	public array 	$type;
	public int 		$amount;

	public static function from(ResourceType $resource, int $amount) : self {
		$self = new self();
		$self->type = [
			'slug' => $resource->value,
			'name' => $resource->getName(),
		];
		$self->amount = $amount;

		return $self;
	}

}