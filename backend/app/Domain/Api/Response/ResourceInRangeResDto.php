<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Region\ResourceRegion;

class ResourceInRangeResDto extends ResDto {

	public int $distance;
	public ResourceRegionResDto $resource_region;

	/** @var ProductionResDto[] */
	public array $amounts;

	public static function from(int $distance, ResourceRegion $resourceRegion, array $amounts) : self {
		$self = new self();
		$self->distance = $distance;
		$self->resource_region = ResourceRegionResDto::from($resourceRegion);
		$self->amounts = $amounts;

		return $self;
	}

}