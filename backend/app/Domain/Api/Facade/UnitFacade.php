<?php declare(strict_types = 1);

namespace App\Domain\Api\Facade;

use App\Domain\Api\Response\UnitResDto;
use App\Domain\Building\Building;
use App\Domain\Building\BuildingRepository;
use App\Domain\Player\Player;
use App\Domain\Unit\Unit;
use App\Domain\Unit\UnitRepository;
use App\Model\Api\Exception\ApiException;
use App\Model\Database\EntityManager;

class UnitFacade {
	private UnitRepository $unitRepository;

	private BuildingRepository $buildingRepository;

	public function __construct(
		EntityManager $entityManager,
	) {
		$this->unitRepository = $entityManager->getRepository(Unit::class);
		$this->buildingRepository = $entityManager->getRepository(Building::class);
	}

	/**
	 * Get a specific unit for a player
	 */
	public function getOneForPlayer(string $uuid, Player $player) : UnitResDto {
		$unit = $this->unitRepository->find($uuid);

		if ($unit === NULL) {
			throw new ApiException('Unit not found');
		}

		// Check if unit belongs to player's building
		if ($unit->getBuilding()->getPlayer()->getId() !== $player->getId()) {
			throw new ApiException('Unit does not belong to player');
		}

		return UnitResDto::from($unit);
	}

	/**
	 * Find all units belonging to player's buildings
	 * @return array<UnitResDto>
	 */
	public function findAllByPlayer(Player $player) : array {
		$units = $this->unitRepository->findAllByPlayer($player);
		return array_map(fn(Unit $unit) => UnitResDto::from($unit), $units);
	}

}