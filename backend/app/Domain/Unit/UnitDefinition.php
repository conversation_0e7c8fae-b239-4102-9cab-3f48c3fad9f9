<?php declare(strict_types = 1);

namespace App\Domain\Unit;

use App\Domain\Combat\TCombatStats;
use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TCreatedAt;
use App\Model\Database\Entity\TId;
use App\Model\Database\Entity\TUpdatedAt;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'game_unit_definition')]
#[ORM\HasLifecycleCallbacks]
class UnitDefinition extends AbstractEntity {

	use TId;
	use TCreatedAt;
	use TUpdatedAt;
	use TCombatStats;
	#[ORM\Column(type: 'string')]
	private string $name;

	#[ORM\Column(type: 'text')]
	private string $description;

	#[ORM\Column(type: 'integer', options: ['default' => 1])]
	private int $size;

	#[ORM\Column(type: 'integer', options: ['default' => 100])]
	private int $baseHealth = 100;

	#[ORM\Column(type: 'integer', options: ['default' => 0])]
	private int $goldCost;

	#[ORM\Column(type: 'integer', options: ['default' => 1])]
	private int $foodCost;

	public function __construct(
		string $name,
		string $description,
		int $foodCost = 1,
		int $goldCost = 0,
		int $size = 1,
	) {
		$this->name = $name;
		$this->description = $description;
		$this->foodCost = $foodCost;
		$this->goldCost = $goldCost;
		$this->size = $size;
	}

	public function getName() : string {
		return $this->name;
	}

	public function setName(string $name) : void {
		$this->name = $name;
	}

	public function getDescription() : string {
		return $this->description;
	}

	public function setDescription(string $description) : void {
		$this->description = $description;
	}

	public function getSize() : int {
		return $this->size;
	}

	public function setSize(int $size) : void {
		$this->size = $size;
	}

	public function getBaseHealth() : int {
		return $this->baseHealth;
	}

	public function setBaseHealth(int $baseHealth) : void {
		$this->baseHealth = $baseHealth;
	}

	public function getGoldCost() : int {
		return $this->goldCost;
	}

	public function setGoldCost(int $goldCost) : void {
		$this->goldCost = $goldCost;
	}

	public function getFoodCost() : int {
		return $this->foodCost;
	}

	public function setFoodCost(int $foodCost) : void {
		$this->foodCost = $foodCost;
	}

}