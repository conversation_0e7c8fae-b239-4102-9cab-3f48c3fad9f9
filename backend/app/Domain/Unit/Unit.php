<?php declare(strict_types = 1);

namespace App\Domain\Unit;

use App\Domain\Building\Building;
use App\Domain\Combat\ICombatable;
use App\Domain\Combat\THealthStats;
use App\Domain\Hero\Hero;
use App\Domain\Player\Player;
use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TCreatedAt;
use App\Model\Database\Entity\TUpdatedAt;
use App\Model\Database\Entity\TUuid;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: UnitRepository::class)]
#[ORM\Table(name: 'game_unit')]
#[ORM\HasLifecycleCallbacks]
class Unit extends AbstractEntity implements ICombatable {
	use TUuid;
	use TCreatedAt;
	use TUpdatedAt;
	use THealthStats;

	#[ORM\ManyToOne(targetEntity: Building::class, inversedBy: 'units')]
	#[ORM\JoinColumn(name: 'building_id', referencedColumnName: 'id')]
	private ?Building $building = null;

	#[ORM\ManyToOne(targetEntity: Hero::class, inversedBy: 'units')]
	#[ORM\JoinColumn(name: 'hero_id', referencedColumnName: 'id')]
	private ?Hero $hero = null;

	#[ORM\ManyToOne(targetEntity: UnitDefinition::class)]
	#[ORM\JoinColumn(name: 'unit_definition_id', nullable: FALSE, onDelete: 'CASCADE')]
	private UnitDefinition $unitDefinition;

	#[ORM\Column(type: 'boolean', options: ['default' => false])]
	private bool $hungry = false;

	public function __construct(UnitDefinition $unitDefinition) {
		$this->unitDefinition = $unitDefinition;
		$this->setHealth($unitDefinition->getBaseHealth());
	}

	public function getBuilding() : Building {
		return $this->building;
	}

	public function setBuilding(?Building $building) : void {
		$this->building = $building;
		$this->setHero(NULL);
	}

	public function getHero() : ?Hero {
		return $this->hero;
	}

	public function setHero(?Hero $hero) : void {
		$this->hero = $hero;
		$this->setBuilding(NULL);
	}

	public function getPlayer() : ?Player {
		return $this->hero?->getPlayer() ?? $this->building?->getPlayer();
	}

	public function getUnitDefinition() : UnitDefinition {
		return $this->unitDefinition;
	}

	public function isHungry() : bool {
		return $this->hungry;
	}

	public function setHungry(bool $hungry) : void {
		$this->hungry = $hungry;
	}

	public function getName() : string {
		return $this->unitDefinition->getName();
	}

	public function getDefence() : int {
		$baseDefence = $this->unitDefinition->getDefence();
		return $this->isHungry() ? (int) ($baseDefence * 0.5) : $baseDefence;
	}

	public function getStrength() : int {
		$baseAttack = $this->unitDefinition->getStrength();
		return $this->isHungry() ? (int) ($baseAttack * 0.5) : $baseAttack;
	}

	public function getAgility() : int {
		$baseAgility = $this->unitDefinition->getAgility();
		return $this->isHungry() ? (int) ($baseAgility * 0.5) : $baseAgility;
	}

	public function getLuck() : int {
		$baseLuck = $this->unitDefinition->getLuck();
		return $this->isHungry() ? (int) ($baseLuck * 0.5) : $baseLuck;
	}

	public function getMaxHealth() : int {
		return $this->unitDefinition->getBaseHealth();
	}
}