{"name": "kokume/gps-game", "description": "", "keywords": [], "type": "project", "license": [], "require": {"php": ">= 8.3", "nette/application": "^3.2.3", "nette/bootstrap": "^3.2", "nette/caching": "^3.2", "nette/database": "^3.2", "nette/di": "^3.2", "nette/forms": "^3.2", "nette/http": "^3.3", "nette/mail": "^4.0", "nette/robot-loader": "^4.0", "nette/security": "^3.2", "nette/utils": "^4.0", "latte/latte": "^3.0", "tracy/tracy": "^2.10", "contributte/apitte": "^0.12.0", "contributte/console": "^0.10.0", "contributte/event-dispatcher": "^v0.9.1", "contributte/event-dispatcher-extra": "^v0.10.1", "contributte/oauth2-server": "^0.4.0", "contributte/webpack": "^2.3", "contributte/scheduler": "^0.8.0", "contributte/phpstan": "^v0.2", "contributte/sentry": "^0.3.0", "ublaboo/datagrid": "7.x-dev", "nettrine/annotations": "^0.7.0", "nettrine/orm": "^0.8.0", "nettrine/dbal": "^0.8.0", "nettrine/cache": "^0.3.0", "nettrine/migrations": "^0.9.1", "nettrine/fixtures": "^0.7.2", "symfony/serializer": "^7.0.3", "symfony/validator": "^7.0.2", "symfony/property-info": "^7.0.6", "symfony/cache": "^7.0.0", "nelmio/alice": "^3.13.5", "ramsey/uuid-doctrine": "^2.1", "jsor/doctrine-postgis": "^2.3", "lucianotonet/groq-php": "^0.0.10", "contributte/forms-bootstrap": "^0.8.3"}, "require-dev": {"nette/tester": "^2.5", "symfony/thanks": "^1", "phpstan/phpstan": "^2.0.2", "phpstan/phpstan-nette": "^2.0.1", "phpstan/phpstan-doctrine": "^2.0.0", "phpunit/phpunit": "^11.5"}, "autoload": {"psr-4": {"App\\": "app", "Database\\": "db"}}, "minimum-stability": "stable", "config": {"allow-plugins": {"symfony/thanks": true, "composer/package-versions-deprecated": true, "dealerdirect/phpcodesniffer-composer-installer": true}}}